import { computed, getCurrentInstance, ref, type CSSProperties } from "vue";
import { useTabsStoreHook } from "@/store/modules/tabs";
import type { MultiTagItem } from "@/store/modules/tabs";
import { useLayoutStoreHook } from "@/store/modules/layout";

/**
 * 标签页视图类型
 */
export interface TagsViewType {
  icon: string;
  text: string;
  divided: boolean;
  disabled: boolean;
  show: boolean;
}

/**
 * 标签页管理hooks
 */
export function useTags() {
  const route = useRoute();
  const router = useRouter();
  const instance = getCurrentInstance();
  const tabsStore = useTabsStoreHook();
  const layoutStore = useLayoutStoreHook();

  const buttonTop = ref(0);
  const buttonLeft = ref(0);
  const translateX = ref(0);
  const visible = ref(false);
  const activeIndex = ref(-1);
  // 当前右键选中的路由信息
  const currentSelect = ref<MultiTagItem>({} as MultiTagItem);
  const isScrolling = ref(false);

  /** 是否隐藏标签页，默认显示 */
  const showTags = ref(false);

  const multiTags = computed(() => {
    return tabsStore.multiTags;
  });

  const tagsViews = ref<Array<TagsViewType>>([
    {
      icon: "el-icon-refresh-right",
      text: "重新加载",
      divided: false,
      disabled: false,
      show: true,
    },
    {
      icon: "el-icon-close",
      text: "关闭当前标签页",
      divided: false,
      disabled: multiTags.value.length > 1 ? false : true,
      show: true,
    },
    {
      icon: "el-icon-back",
      text: "关闭左侧标签页",
      divided: true,
      disabled: multiTags.value.length > 1 ? false : true,
      show: true,
    },
    {
      icon: "el-icon-right",
      text: "关闭右侧标签页",
      divided: false,
      disabled: multiTags.value.length > 1 ? false : true,
      show: true,
    },
    {
      icon: "el-icon-remove",
      text: "关闭其他标签页",
      divided: true,
      disabled: multiTags.value.length > 2 ? false : true,
      show: true,
    },
    {
      icon: "el-icon-minus",
      text: "关闭全部标签页",
      divided: false,
      disabled: multiTags.value.length > 1 ? false : true,
      show: true,
    },
    {
      icon: "el-icon-full-screen",
      text: "内容区全屏",
      divided: true,
      disabled: false,
      show: true,
    },
  ]);

  /**
   * 判断条件处理
   */
  function conditionHandle(item: any, previous: any, next: any) {
    if (Object.keys(route.query).length > 0) {
      return JSON.stringify(route.query) === JSON.stringify(item.query)
        ? previous
        : next;
    } else if (Object.keys(route.params).length > 0) {
      return JSON.stringify(route.params) === JSON.stringify(item.params)
        ? previous
        : next;
    } else {
      return route.path === item.path ? previous : next;
    }
  }

  const iconIsActive = computed(() => {
    return (item: any, index: number) => {
      if (index === 0) return;
      return conditionHandle(item, true, false);
    };
  });

  const linkIsActive = computed(() => {
    return (item: any) => {
      return conditionHandle(item, "is-active", "");
    };
  });

  const scheduleIsActive = computed(() => {
    return (item: any) => {
      return conditionHandle(item, "schedule-active", "");
    };
  });

  const getTabStyle = computed((): CSSProperties => {
    return {
      transform: `translateX(${translateX.value}px)`,
      transition: isScrolling.value ? "none" : "transform 0.5s ease-in-out",
    };
  });

  const getContextMenuStyle = computed((): CSSProperties => {
    return { left: buttonLeft.value + "px", top: buttonTop.value + "px" };
  });

  const closeMenu = () => {
    visible.value = false;
  };

  /** 鼠标移入添加激活样式 */
  function onMouseenter(index: number) {
    if (index) activeIndex.value = index;
    // 这里可以添加鼠标悬停效果
  }

  /** 鼠标移出恢复默认样式 */
  function onMouseleave(_index: number) {
    activeIndex.value = -1;
    // 这里可以添加鼠标离开效果
  }

  /**
   * 内容区全屏切换
   */
  function onContentFullScreen() {
    layoutStore.toggleCollapse();
  }

  /**
   * 国际化转换（简化版）
   */
  function transformI18n(text: string) {
    return text;
  }

  return {
    route,
    router,
    visible,
    showTags,
    instance,
    multiTags,
    tagsViews,
    buttonTop,
    buttonLeft,
    translateX,
    activeIndex,
    getTabStyle,
    isScrolling,
    iconIsActive,
    linkIsActive,
    currentSelect,
    scheduleIsActive,
    getContextMenuStyle,
    closeMenu,
    onMouseenter,
    onMouseleave,
    transformI18n,
    onContentFullScreen,
  };
}
