<script setup lang="ts">
import type { MultiTagItem } from "@/store/modules/tabs";
import { useTabsStoreHook } from "@/store/modules/tabs";
import { useTags } from "@/layout/hooks/useTag";

/**
 * 多标签页组件
 * 显示已打开的页面标签，支持切换、关闭、右键菜单等功能
 */
const {
  route,
  router,
  visible,
  showTags,
  instance,
  multiTags,
  tagsViews,
  buttonTop,
  buttonLeft,
  translateX,
  activeIndex,
  getTabStyle,
  isScrolling,
  iconIsActive,
  linkIsActive,
  currentSelect,
  scheduleIsActive,
  getContextMenuStyle,
  closeMenu,
  onMouseenter,
  onMouseleave,
  transformI18n,
  onContentFullScreen,
} = useTags();

const layoutStore = useLayoutStoreHook();
const tabsStore = useTabsStoreHook();

// 计算tag-views的样式
const tagsViewStyle = computed(() => {
  return {
    left: layoutStore.getCurrentAsideWidth,
  };
});

// DOM引用
const tabDom = ref();
const containerDom = ref();
const scrollbarDom = ref();
const contextmenuRef = ref();
const isShowArrow = ref(false);

/**
 * 动态标签视图
 */
const dynamicTagView = async () => {
  await nextTick();
  const index = multiTags.value.findIndex((item) => {
    if (Object.keys(route.query).length > 0) {
      return JSON.stringify(route.query) === JSON.stringify(item.query);
    } else if (Object.keys(route.params).length > 0) {
      return JSON.stringify(route.params) === JSON.stringify(item.params);
    } else {
      return route.path === item.path;
    }
  });
  moveToView(index);
};

/**
 * 移动到视图
 */
const moveToView = async (index: number): Promise<void> => {
  await nextTick();
  const tabNavPadding = 10;
  if (!instance?.refs || !instance.refs["dynamic" + index]) return;

  const tabItemEl = instance.refs["dynamic" + index][0] as HTMLElement;
  const tabItemElOffsetLeft = tabItemEl?.offsetLeft;
  const tabItemOffsetWidth = tabItemEl?.offsetWidth;

  // 标签页导航栏可视长度（不包含溢出部分）
  const scrollbarDomWidth = scrollbarDom.value
    ? scrollbarDom.value?.offsetWidth
    : 0;
  // 已有标签页总长度（包含溢出部分）
  const tabDomWidth = tabDom.value ? tabDom.value?.offsetWidth : 0;

  scrollbarDomWidth <= tabDomWidth
    ? (isShowArrow.value = true)
    : (isShowArrow.value = false);

  if (tabDomWidth < scrollbarDomWidth || tabItemElOffsetLeft === 0) {
    translateX.value = 0;
  } else if (tabItemElOffsetLeft < -translateX.value) {
    // 标签在可视区域左侧
    translateX.value = -tabItemElOffsetLeft + tabNavPadding;
  } else if (
    tabItemElOffsetLeft > -translateX.value &&
    tabItemElOffsetLeft + tabItemOffsetWidth <
      -translateX.value + scrollbarDomWidth
  ) {
    // 标签在可视区域
    translateX.value = Math.min(
      0,
      scrollbarDomWidth -
        tabItemOffsetWidth -
        tabItemElOffsetLeft -
        tabNavPadding
    );
  } else {
    // 标签在可视区域右侧
    translateX.value = -(
      tabItemElOffsetLeft -
      (scrollbarDomWidth - tabNavPadding - tabItemOffsetWidth)
    );
  }
};

/**
 * 处理滚动
 */
const handleScroll = (offset: number): void => {
  const scrollbarDomWidth = scrollbarDom.value
    ? scrollbarDom.value?.offsetWidth
    : 0;
  const tabDomWidth = tabDom.value ? tabDom.value.offsetWidth : 0;

  if (offset > 0) {
    translateX.value = Math.min(0, translateX.value + offset);
  } else {
    if (scrollbarDomWidth < tabDomWidth) {
      if (translateX.value >= -(tabDomWidth - scrollbarDomWidth)) {
        translateX.value = Math.max(
          translateX.value + offset,
          scrollbarDomWidth - tabDomWidth
        );
      }
    } else {
      translateX.value = 0;
    }
  }
  isScrolling.value = false;
};

/**
 * 处理鼠标滚轮
 */
const handleWheel = (event: WheelEvent): void => {
  isScrolling.value = true;
  const scrollIntensity = Math.abs(event.deltaX) + Math.abs(event.deltaY);
  let offset = 0;
  if (event.deltaX < 0) {
    offset = scrollIntensity > 0 ? scrollIntensity : 100;
  } else {
    offset = scrollIntensity > 0 ? -scrollIntensity : -100;
  }
  smoothScroll(offset);
};

/**
 * 平滑滚动
 */
const smoothScroll = (offset: number): void => {
  const scrollAmount = 20; // 每帧滚动的距离
  let remaining = Math.abs(offset);

  const scrollStep = () => {
    const scrollOffset = Math.sign(offset) * Math.min(scrollAmount, remaining);
    handleScroll(scrollOffset);
    remaining -= Math.abs(scrollOffset);

    if (remaining > 0) {
      requestAnimationFrame(scrollStep);
    }
  };

  requestAnimationFrame(scrollStep);
};

/**
 * 刷新路由
 */
function onFresh() {
  const { fullPath, query } = route;
  router.replace({
    path: "/redirect" + fullPath,
    query,
  });
}

/**
 * 删除动态标签
 */
function deleteDynamicTag(obj: MultiTagItem, current: string, tag?: string) {
  const valueIndex: number = multiTags.value.findIndex((item: MultiTagItem) => {
    if (item.query && obj.query) {
      if (item.path === obj.path) {
        return JSON.stringify(item.query) === JSON.stringify(obj.query);
      }
    } else if (item.params && obj.params) {
      if (item.path === obj.path) {
        return JSON.stringify(item.params) === JSON.stringify(obj.params);
      }
    } else {
      return item.path === obj.path;
    }
  });

  const spliceRoute = (
    startIndex?: number,
    length?: number,
    other?: boolean
  ): void => {
    if (other) {
      const affixTags = multiTags.value.filter((tag) => tag.meta.affix);
      tabsStore.handleTags("equal", [...affixTags, obj]);
    } else {
      tabsStore.handleTags("splice", "", {
        startIndex,
        length,
      });
    }
    dynamicTagView();
  };

  if (tag === "other") {
    spliceRoute(1, 1, true);
  } else if (tag === "left") {
    spliceRoute(1, valueIndex - 1);
  } else if (tag === "right") {
    spliceRoute(valueIndex + 1, multiTags.value.length);
  } else {
    // 从当前匹配到的路径中删除
    spliceRoute(valueIndex, 1);
  }

  const newRoute = tabsStore.handleTags("slice");
  if (current === route.path) {
    // 如果删除当前激活tag就自动切换到最后一个tag
    if (tag === "left") return;
    if (newRoute[0]?.query) {
      router.push({ name: newRoute[0].name, query: newRoute[0].query });
    } else if (newRoute[0]?.params) {
      router.push({ name: newRoute[0].name, params: newRoute[0].params });
    } else {
      router.push({ path: newRoute[0].path });
    }
  } else {
    if (!multiTags.value.length) return;
    if (multiTags.value.some((item) => item.path === route.path)) return;
    if (newRoute[0]?.query) {
      router.push({ name: newRoute[0].name, query: newRoute[0].query });
    } else if (newRoute[0]?.params) {
      router.push({ name: newRoute[0].name, params: newRoute[0].params });
    } else {
      router.push({ path: newRoute[0].path });
    }
  }
}

/**
 * 删除菜单
 */
function deleteMenu(item: MultiTagItem, tag?: string) {
  deleteDynamicTag(item, item.path, tag);
}

/**
 * 处理右键菜单点击
 */
function onClickDrop(key: number, item: any, selectRoute?: MultiTagItem) {
  if (item && item.disabled) return;

  let selectTagRoute: MultiTagItem;
  if (selectRoute) {
    selectTagRoute = {
      path: selectRoute.path,
      meta: selectRoute.meta,
      name: selectRoute.name,
      query: selectRoute?.query,
      params: selectRoute?.params,
    };
  } else {
    selectTagRoute = {
      path: route.path,
      meta: route.meta,
      name: route.name,
      query: route.query,
      params: route.params,
    } as MultiTagItem;
  }

  // 当前路由信息
  switch (key) {
    case 0:
      // 刷新路由
      onFresh();
      break;
    case 1:
      // 关闭当前标签页
      deleteMenu(selectTagRoute);
      break;
    case 2:
      // 关闭左侧标签页
      deleteMenu(selectTagRoute, "left");
      break;
    case 3:
      // 关闭右侧标签页
      deleteMenu(selectTagRoute, "right");
      break;
    case 4:
      // 关闭其他标签页
      deleteMenu(selectTagRoute, "other");
      break;
    case 5:
      // 关闭全部标签页
      tabsStore.handleTags("splice", "", {
        startIndex: 1,
        length: multiTags.value.length,
      });
      router.push("/welcome");
      break;
    case 6:
      // 内容区全屏
      onContentFullScreen();
      break;
  }

  setTimeout(() => {
    showMenuModel(route.fullPath, route.query);
  });
}

/**
 * 处理命令
 */
function handleCommand(command: any) {
  const { key, item } = command;
  onClickDrop(key, item);
}

/**
 * 触发右键中菜单的点击事件
 */
function selectTag(key: number, item: any) {
  closeMenu();
  onClickDrop(key, item, currentSelect.value);
}

/**
 * 显示菜单
 */
function showMenus(value: boolean) {
  [1, 2, 3, 4, 5].forEach((v) => {
    tagsViews.value[v].show = value;
  });
}

/**
 * 禁用菜单
 */
function disabledMenus(value: boolean) {
  [1, 2, 3, 4, 5].forEach((v) => {
    tagsViews.value[v].disabled = value;
  });
}

/**
 * 显示菜单模型
 */
function showMenuModel(
  currentPath: string,
  query: object = {},
  refresh = false
) {
  const allRoute = multiTags.value;
  const routeLength = multiTags.value.length;
  let currentIndex = -1;

  if (Object.keys(query).length === 0) {
    currentIndex = allRoute.findIndex((v) => v.path === currentPath);
  } else {
    currentIndex = allRoute.findIndex(
      (v) => JSON.stringify(v.query) === JSON.stringify(query)
    );
  }

  showMenus(true);

  if (refresh) {
    tagsViews.value[0].show = true;
  }

  if (currentIndex === 1 && routeLength !== 2) {
    // 左侧的菜单是顶级菜单，右侧存在别的菜单
    tagsViews.value[2].show = false;
    [1, 3, 4, 5].forEach((v) => {
      tagsViews.value[v].disabled = false;
    });
    tagsViews.value[2].disabled = true;
  } else if (currentIndex === 1 && routeLength === 2) {
    disabledMenus(false);
    // 左侧的菜单是顶级菜单，右侧不存在别的菜单
    [2, 3, 4].forEach((v) => {
      tagsViews.value[v].show = false;
      tagsViews.value[v].disabled = true;
    });
  } else if (routeLength - 1 === currentIndex && currentIndex !== 0) {
    // 当前路由是所有路由中的最后一个
    tagsViews.value[3].show = false;
    [1, 2, 4, 5].forEach((v) => {
      tagsViews.value[v].disabled = false;
    });
    tagsViews.value[3].disabled = true;
  } else if (currentIndex === 0) {
    // 当前路由为顶级菜单
    disabledMenus(true);
  } else {
    disabledMenus(false);
  }
}

/**
 * 打开菜单
 */
function openMenu(tag: MultiTagItem, e: MouseEvent) {
  closeMenu();

  if (tag.path === "/welcome") {
    // 右键菜单为顶级菜单，只显示刷新
    showMenus(false);
    tagsViews.value[0].show = true;
  } else if (route.path !== tag.path && route.name !== tag.name) {
    // 右键菜单不匹配当前路由，隐藏刷新
    tagsViews.value[0].show = false;
    showMenuModel(tag.path, tag.query);
  } else if (multiTags.value.length === 2 && route.path !== tag.path) {
    showMenus(true);
    // 只有两个标签时不显示关闭其他标签页
    tagsViews.value[4].show = false;
  } else if (route.path === tag.path) {
    // 右键当前激活的菜单
    showMenuModel(tag.path, tag.query, true);
  }

  currentSelect.value = tag;
  const menuMinWidth = 140;
  const offsetLeft = containerDom.value.getBoundingClientRect().left;
  const offsetWidth = containerDom.value.offsetWidth;
  const maxLeft = offsetWidth - menuMinWidth;
  const left = e.clientX - offsetLeft + 5;

  if (left > maxLeft) {
    buttonLeft.value = maxLeft;
  } else {
    buttonLeft.value = left;
  }

  buttonTop.value = e.clientY - 40;

  nextTick(() => {
    visible.value = true;
  });
}

/**
 * 触发tags标签切换
 */
function tagOnClick(item: MultiTagItem) {
  const { name, path } = item;
  if (name) {
    if (item.query) {
      router.push({
        name,
        query: item.query,
      });
    } else if (item.params) {
      router.push({
        name,
        params: item.params,
      });
    } else {
      router.push({ name });
    }
  } else {
    router.push({ path });
  }
}

// 监听路由变化
watch(route, () => {
  activeIndex.value = -1;
  dynamicTagView();
});

onMounted(() => {
  if (!instance) return;

  // 根据当前路由初始化操作标签页的禁用状态
  showMenuModel(route.fullPath);

  // 监听标签页变化
  dynamicTagView();
});

// 监听路由变化，添加标签
watch(
  () => route,
  (newRoute) => {
    if (newRoute.meta?.history !== false) {
      tabsStore.addTab(newRoute);
    }
  },
  { immediate: true, deep: true }
);

// 点击其他地方关闭右键菜单
onMounted(() => {
  document.addEventListener("click", closeMenu);
});

onUnmounted(() => {
  document.removeEventListener("click", closeMenu);
});
</script>

<template>
  <div ref="containerDom" class="tags-view" :style="tagsViewStyle">
    <span v-show="isShowArrow" class="arrow-left">
      <el-icon @click="handleScroll(200)">
        <ArrowLeft />
      </el-icon>
    </span>

    <div
      ref="scrollbarDom"
      class="scroll-container"
      @wheel.prevent="handleWheel"
    >
      <div ref="tabDom" class="tab select-none" :style="getTabStyle">
        <div
          v-for="(item, index) in multiTags"
          :ref="'dynamic' + index"
          :key="index"
          :class="['scroll-item is-closable', linkIsActive(item)]"
          @contextmenu.prevent="openMenu(item, $event)"
          @mouseenter.prevent="onMouseenter(index)"
          @mouseleave.prevent="onMouseleave(index)"
          @click="tagOnClick(item)"
        >
          <span class="tag-title">
            {{ transformI18n(item.meta.title) }}
          </span>
          <span
            v-if="
              iconIsActive(item, index) ||
              (index === activeIndex && index !== 0)
            "
            class="el-icon-close"
            @click.stop="deleteMenu(item)"
          >
            <el-icon>
              <Close />
            </el-icon>
          </span>
          <span :ref="'schedule' + index" :class="[scheduleIsActive(item)]" />
        </div>
      </div>
    </div>

    <span v-show="isShowArrow" class="arrow-right">
      <el-icon @click="handleScroll(-200)">
        <ArrowRight />
      </el-icon>
    </span>

    <!-- 右键菜单按钮 -->
    <transition name="el-zoom-in-top">
      <ul
        v-show="visible"
        ref="contextmenuRef"
        :key="Math.random()"
        :style="getContextMenuStyle"
        class="contextmenu"
      >
        <div
          v-for="(item, key) in tagsViews.slice(0, 6)"
          :key="key"
          style="display: flex; align-items: center"
        >
          <li v-if="item.show" @click="selectTag(key, item)">
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            {{ transformI18n(item.text) }}
          </li>
        </div>
      </ul>
    </transition>

    <!-- 右侧功能按钮 -->
    <el-dropdown
      trigger="click"
      placement="bottom-end"
      @command="handleCommand"
    >
      <span class="arrow-down">
        <el-icon>
          <ArrowDown />
        </el-icon>
      </span>
      <template #dropdown>
        <el-dropdown-menu>
          <el-dropdown-item
            v-for="(item, key) in tagsViews"
            :key="key"
            :command="{ key, item }"
            :divided="item.divided"
            :disabled="item.disabled"
          >
            <el-icon>
              <component :is="item.icon" />
            </el-icon>
            {{ transformI18n(item.text) }}
          </el-dropdown-item>
        </el-dropdown-menu>
      </template>
    </el-dropdown>
  </div>
</template>

<style lang="scss" scoped>
.tags-view {
  position: fixed;
  top: 64px; // Header高度
  left: 240px; // 侧边栏宽度
  right: 0;
  height: 40px;
  background-color: #fff;
  border-bottom: 1px solid #e5e7eb;
  z-index: 999;
  transition: left 0.3s ease;
  display: flex;
  align-items: center;
  padding: 0 10px;

  .arrow-left,
  .arrow-right {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    cursor: pointer;
    color: #666;

    &:hover {
      color: #1890ff;
    }
  }

  .scroll-container {
    flex: 1;
    overflow: hidden;
    margin: 0 10px;

    .tab {
      display: flex;
      transition: transform 0.5s ease-in-out;

      .scroll-item {
        display: flex;
        align-items: center;
        padding: 0 12px;
        height: 32px;
        background-color: #fff;
        border: 1px solid #d9d9d9;
        border-radius: 2px;
        margin-right: 4px;
        cursor: pointer;
        user-select: none;
        white-space: nowrap;
        position: relative;
        transition: all 0.3s ease;

        &:hover {
          background-color: #f5f5f5;
          border-color: #d9d9d9;
        }

        &.is-active {
          background-color: #1890ff;
          border-color: #1890ff;
          color: #fff;

          .tag-title {
            color: #fff;
          }
        }

        .tag-title {
          font-size: 13px;
          margin-right: 8px;
        }

        .el-icon-close {
          display: flex;
          align-items: center;
          justify-content: center;
          width: 16px;
          height: 16px;
          border-radius: 50%;
          font-size: 12px;

          &:hover {
            background-color: rgba(0, 0, 0, 0.1);
          }
        }

        .schedule-active {
          position: absolute;
          bottom: 0;
          left: 0;
          right: 0;
          height: 2px;
          background-color: #1890ff;
        }
      }
    }
  }

  .arrow-down {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 20px;
    height: 20px;
    cursor: pointer;
    color: #666;

    &:hover {
      color: #1890ff;
    }
  }
}

// 右键菜单样式
.contextmenu {
  position: fixed;
  background-color: #fff;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
  z-index: 9999;
  min-width: 140px;
  padding: 4px 0;

  li {
    display: flex;
    align-items: center;
    padding: 8px 12px;
    font-size: 13px;
    cursor: pointer;
    transition: background-color 0.2s ease;

    &:hover {
      background-color: #f3f4f6;
    }

    .el-icon {
      margin-right: 8px;
      font-size: 14px;
    }
  }
}

// 动画效果
.el-zoom-in-top-enter-active,
.el-zoom-in-top-leave-active {
  opacity: 1;
  transform: scaleY(1);
  transition: transform 0.3s cubic-bezier(0.23, 1, 0.32, 1),
    opacity 0.3s cubic-bezier(0.23, 1, 0.32, 1);
  transform-origin: center top;
}

.el-zoom-in-top-enter-from,
.el-zoom-in-top-leave-to {
  opacity: 0;
  transform: scaleY(0);
}

// 侧边栏折叠状态适配
.aside-collapsed {
  .tags-view {
    left: 64px !important;
  }
}
</style>
