<template>
    <div
        class="risk-policy-top-bar"
        :style="{
            height: layoutStore.header.height,
            color: layoutStore.header.textColor,
            backgroundColor: layoutStore.header.backgroundColor,
        }"
    >
        <div class="left">
            <!-- <img src="@/assets/img/logo.jpg" @click="goHome" style="height: 30px" /> -->
            <!-- TODO: logo暂时没有 -->
            <img src="@/assets/logo.png" @click="goHome" style="height: 30px" />
            <div class="title">
                <div class="title-cn">中国（海南）国际贸易单一窗口</div>
                <div class="title-en">China International Trade Single Window</div>
            </div>
            <el-divider direction="vertical"></el-divider>
            <span class="system-name">{{ appTitle }}</span>
        </div>
        <div class="center" v-if="false">
            <slot name="content"></slot>
            <p style="cursor: pointer; color: #909399" @click="handleTo('/system/variablecenter')">
                <i class="el-icon-setting"></i>
                <span style="margin: 0 8px">系统设置</span>
            </p>
        </div>
        <div class="right">
            <!-- <el-popover
        placement="bottom"
        popper-class="org-popper"
        width="314"
        trigger="click">
        <div class="link-list-box">
          <div class="link-list-item" v-for="(lit, lix) in linkList" :key="lit.name+'-item-'+lix" @click="enterLink(lit)">
            <div>
              <img :src="lit.iconUrl" />
              <span>{{lit.name}}</span>
            </div>
          </div>
        </div>
        <div slot="reference" class="setting-icon customer-org">
          <i class="el-icon-s-grid" style="color: #8A8A8A;font-size: 20px;"></i>
        </div>
      </el-popover> -->
            <!-- 消息通知 -->
            <el-popover
                v-if="true"
                placement="bottom"
                width="380"
                popper-class="my-popover"
                trigger="click"
                v-model="visible"
            >
                <div
                    class="right-search"
                    style="margin: 0 10px"
                    slot="reference"
                    @click="openMessage"
                >
                    <el-badge
                        v-if="remainingCount > 0"
                        :value="remainingCount"
                        :max="99"
                        style="line-height: normal"
                    >
                        <svg
                            style="cursor: pointer"
                            width="17"
                            height="17"
                            t="1618561774520"
                            class="icon"
                            viewBox="0 0 1024 1024"
                            version="1.1"
                            xmlns="http://www.w3.org/2000/svg"
                            p-id="19059"
                            xmlns:xlink="http://www.w3.org/1999/xlink"
                        >
                            <path
                                d="M721.338182 350.859636a64.512 64.512 0 0 1 64.512 64.488728 64.512 64.512 0 0 1-129.000727 0 64.488727 64.488727 0 0 1 64.488727-64.488728zM495.592727 350.859636a64.465455 64.465455 0 1 1 0 128.977455 64.465455 64.465455 0 0 1 0-128.977455zM269.847273 350.859636a64.512 64.512 0 1 1 0 129.000728 64.512 64.512 0 0 1 0-129.000728z"
                                p-id="19060"
                            ></path>
                            <path
                                d="M427.264 930.769455a32.116364 32.116364 0 0 1-28.299636-16.756364l-109.544728-192.069818a32.186182 32.186182 0 0 1 12.032-43.962182 32.116364 32.116364 0 0 1 43.962182 12.032l81.850182 143.429818 81.850182-143.429818c8.843636-15.499636 28.509091-20.945455 43.985454-12.032 15.476364 8.797091 20.852364 28.485818 12.032 43.962182l-109.568 192.069818a32.302545 32.302545 0 0 1-28.229818 16.756364h-0.069818z"
                                p-id="19061"
                            ></path>
                            <path
                                d="M837.934545 737.024H540.439273c-17.826909 0-32.256-14.405818-32.256-32.256s14.429091-32.256 32.256-32.256h297.495272c42.146909 0 76.404364-32.814545 76.404364-73.146182V230.795636c0-40.308364-34.280727-73.076364-76.404364-73.076363H186.042182c-42.123636 0-76.381091 32.768-76.381091 73.076363v368.570182c0 40.308364 34.257455 73.146182 76.381091 73.146182h129.419636c17.826909 0 32.256 14.405818 32.256 32.256s-14.429091 32.256-32.256 32.256H186.042182c-77.684364 0-140.893091-61.719273-140.893091-137.634909V230.795636c0-75.869091 63.208727-137.588364 140.893091-137.588363h651.892363c77.684364 0 140.916364 61.719273 140.916364 137.588363v368.570182c0 75.938909-63.232 137.658182-140.916364 137.658182z"
                                p-id="19062"
                            ></path>
                        </svg>
                    </el-badge>
                    <svg
                        v-else
                        style="cursor: pointer"
                        width="17"
                        height="17"
                        t="1618561774520"
                        class="icon"
                        viewBox="0 0 1024 1024"
                        version="1.1"
                        xmlns="http://www.w3.org/2000/svg"
                        p-id="19059"
                        xmlns:xlink="http://www.w3.org/1999/xlink"
                    >
                        <path
                            d="M721.338182 350.859636a64.512 64.512 0 0 1 64.512 64.488728 64.512 64.512 0 0 1-129.000727 0 64.488727 64.488727 0 0 1 64.488727-64.488728zM495.592727 350.859636a64.465455 64.465455 0 1 1 0 128.977455 64.465455 64.465455 0 0 1 0-128.977455zM269.847273 350.859636a64.512 64.512 0 1 1 0 129.000728 64.512 64.512 0 0 1 0-129.000728z"
                            p-id="19060"
                        ></path>
                        <path
                            d="M427.264 930.769455a32.116364 32.116364 0 0 1-28.299636-16.756364l-109.544728-192.069818a32.186182 32.186182 0 0 1 12.032-43.962182 32.116364 32.116364 0 0 1 43.962182 12.032l81.850182 143.429818 81.850182-143.429818c8.843636-15.499636 28.509091-20.945455 43.985454-12.032 15.476364 8.797091 20.852364 28.485818 12.032 43.962182l-109.568 192.069818a32.302545 32.302545 0 0 1-28.229818 16.756364h-0.069818z"
                            p-id="19061"
                        ></path>
                        <path
                            d="M837.934545 737.024H540.439273c-17.826909 0-32.256-14.405818-32.256-32.256s14.429091-32.256 32.256-32.256h297.495272c42.146909 0 76.404364-32.814545 76.404364-73.146182V230.795636c0-40.308364-34.280727-73.076364-76.404364-73.076363H186.042182c-42.123636 0-76.381091 32.768-76.381091 73.076363v368.570182c0 40.308364 34.257455 73.146182 76.381091 73.146182h129.419636c17.826909 0 32.256 14.405818 32.256 32.256s-14.429091 32.256-32.256 32.256H186.042182c-77.684364 0-140.893091-61.719273-140.893091-137.634909V230.795636c0-75.869091 63.208727-137.588364 140.893091-137.588363h651.892363c77.684364 0 140.916364 61.719273 140.916364 137.588363v368.570182c0 75.938909-63.232 137.658182-140.916364 137.658182z"
                            p-id="19062"
                        ></path>
                    </svg>
                </div>
                <template>
                    <div v-loading="messageLoading" class="my-popover-box">
                        <div style="padding: 8px 30px 0">
                            <el-tabs v-model="activeName" @tab-click="tabClick">
                                <el-tab-pane label="通知" name="1"></el-tab-pane>
                                <el-tab-pane label="项目" name="2"></el-tab-pane>
                            </el-tabs>
                        </div>
                        <vue-scroll
                            :ops="{
                                bar: {
                                    background: '#cecece',
                                    onlyShowBarOnScroll: false,
                                    minSize: 0.1,
                                },
                            }"
                            style="height: 565px"
                        >
                            <div
                                class="top-tab"
                                v-for="(item, index) in insideList"
                                :key="index"
                                @click="changeDetail(item.id, index, 'insideList', item)"
                                :style="item.readIs || readStatus ? 'color:#ccc;' : ''"
                            >
                                <div style="display: flex; align-items: center; height: 44px">
                                    <svg
                                        style="width: 30px; height: 30px; margin-right: 16px"
                                        aria-hidden="true"
                                    >
                                        <use xlink:href="#icon-gerentousudengji"></use>
                                    </svg>
                                    <div style="height: 40px">
                                        <div style="margin-top: 2px">{{ item.subClass }}</div>
                                        <div
                                            :title="item.msgContent.title"
                                            style="
                                                width: 180px;
                                                overflow: hidden;
                                                white-space: nowrap;
                                                text-overflow: ellipsis;
                                                font-size: 12px;
                                            "
                                        >
                                            {{ item.msgContent.title }}
                                        </div>
                                    </div>
                                    <span class="top-tab-span-span">{{
                                        item.createTime | formatLogTime
                                    }}</span>
                                </div>
                            </div>
                            <el-empty v-if="insideList && insideList.length === 0"></el-empty>
                        </vue-scroll>
                        <div class="bottom-txt">
                            <el-divider></el-divider>
                            <div class="bottom-all">
                                <div :v-model="readStatus" @click="editreadStatus()">
                                    <span
                                        style="font-size: 14px"
                                        class="right-tabhover"
                                        :style="readStatus ? 'color:#ccc;' : ''"
                                        >全部标记为已读</span
                                    >
                                </div>
                                <div @click="allnotice">所有消息</div>
                            </div>
                        </div>
                    </div>
                </template>
            </el-popover>
            <!-- 所有消息 -->
            <el-dialog
                title="所有消息"
                :visible.sync="connectVisible"
                append-to-body
                width="1090px"
                class="default-dialog"
                custom-class="customheight dialog-center message-dialog"
                :before-close="handleConnectClose"
            >
                <div class="right-tab">
                    <div class="right-tabsize" :v-model="readStatus" @click="editreadStatus()">
                        <i class="el-icon-success" style="color: #3abb07"></i>
                        <span
                            style="font-size: 14px"
                            class="right-tabhover"
                            :style="readStatus ? 'color:#ccc;' : ''"
                            >全部标记为已读</span
                        >
                    </div>
                </div>
                <vue-scroll
                    :ops="{
                        bar: { background: '#cecece', onlyShowBarOnScroll: false, minSize: 0.1 },
                    }"
                    style="height: 626px"
                >
                    <div
                        class="top-tab"
                        v-for="(item, index) in noList"
                        :key="index"
                        @click="changeDetail(item.id, index, 'noList', item)"
                        :style="item.readIs || readStatus ? 'color:#ccc;' : ''"
                    >
                        <div style="display: flex">
                            <div style="display: flex">
                                <span class="top-tab-span">{{ item.msgContent.title }}</span>
                                <span class="top-tab-span-span">{{ item.createTime }}</span>
                            </div>
                        </div>
                        <el-divider></el-divider>
                        <el-empty v-if="noList && noList.length === 0"></el-empty>
                    </div>
                </vue-scroll>
            </el-dialog>
            <!-- 消息详情 -->
            <el-dialog
                title="消息详情"
                :visible.sync="connectVisibles"
                append-to-body
                width="1100px"
                class="default-dialog"
                custom-class=" dialog-center customheight"
                :before-close="handleConnectCloses"
            >
                <div class="message-detail">
                    <div class="left" v-loading="messageLoading">
                        <vue-scroll
                            :ops="{
                                bar: {
                                    background: 'rgba(206,206,206,0)',
                                    onlyShowBarOnScroll: false,
                                    minSize: 0.1,
                                },
                            }"
                            style="height: 535px"
                        >
                            <div
                                class="top-tab"
                                v-for="(item, index) in insideList"
                                :key="index"
                                @click="selectDetail(item.id, index, 'insideList', item)"
                                :style="item.readIs || readStatus ? 'color:#ccc;' : ''"
                            >
                                <div style="display: flex; align-items: center; height: 44px">
                                    <svg
                                        style="width: 30px; height: 30px; margin-right: 16px"
                                        aria-hidden="true"
                                    >
                                        <use xlink:href="#icon-gerentousudengji"></use>
                                    </svg>
                                    <div style="height: 40px">
                                        <div style="margin-top: 2px">{{ item.subClass }}</div>
                                        <div
                                            :title="item.msgContent.title"
                                            style="
                                                width: 180px;
                                                overflow: hidden;
                                                white-space: nowrap;
                                                text-overflow: ellipsis;
                                            "
                                        >
                                            {{ item.msgContent.title }}
                                        </div>
                                    </div>
                                    <span class="top-tab-span-span">{{
                                        item.createTime | formatLogTime
                                    }}</span>
                                </div>
                            </div>
                            <el-empty v-if="insideList && insideList.length === 0"></el-empty>
                        </vue-scroll>
                        <div class="left-pagination">
                            <el-pagination
                                layout="prev, pager, next"
                                :page-size="page.size"
                                :pager-count="5"
                                :current-page="page.current"
                                @current-change="handleCurrentChange"
                                :total="page.total"
                            >
                            </el-pagination>
                        </div>
                    </div>
                    <div v-loading="noDetailLoading" v-if="noDetailList.msgContent" class="right">
                        <div class="right-title">{{ noDetailList.msgContent.title }}</div>
                        <section
                            v-if="noDetailList.msgContent.content"
                            v-html="noDetailList.msgContent.content"
                        ></section>
                    </div>
                </div>
            </el-dialog>
            <!-- <div
                class="right-search"
                @click="goHome('/search')"
                style="margin-right: 13px"
                v-if="alreadyLogin && false"
            >
                <i class="iconfont icon-sousuo1" style="color: #7dbcf8"></i>
            </div>
            <span class="help-center" @click="goHelp" v-if="false">帮助中心</span>
            <jvs-button type="primary" v-if="!alreadyLogin && false" @click="goLogin"
                >登录</jvs-button
            > -->

            <!-- <p slot="reference" class="user-info-tool" v-if="alreadyLogin">
                <span style="margin-left: 0">{{ userInfo.realName }}</span>
                <img :src="userInfo.headImg ? userInfo.headImg : userImg" />
            </p> -->

            <el-popover v-if="alreadyLogin" placement="bottom-start" width="50" trigger="click">
                <div class="top-tool-bar">
                    <!-- <p @click="goInfo('userManager')">
                        <i
                            class="el-icon-user-solid"
                            style="
                                padding: 0px 10px;
                                color: rgb(121 132 158);
                                font-size: 16px;
                                margin-left: 10px;
                            "
                        />
                        <span>个人中心</span>
                    </p>
                    <p @click="goHelp">
                        <i
                            class="el-icon-question"
                            style="
                                padding: 0px 10px;
                                color: rgb(121 132 158);
                                font-size: 16px;
                                margin-left: 10px;
                            "
                        />
                        <span>帮助中心</span>
                    </p>
                    <p
                        class="menu-item"
                        v-if="changeTenantsList && changeTenantsList.length > 0"
                        @click="switchTenant"
                    >
                        <i
                            class="el-icon-s-custom"
                            style="
                                padding: 0px 10px;
                                color: rgb(121 132 158);
                                font-size: 16px;
                                margin-left: 10px;
                            "
                        />
                        <span>切换租户</span>
                    </p> -->
                    <p @click="loginout">
                        <!-- <img :src="outImg" style="padding: 0px 10px; margin-left: 10px" /> -->
                        <i
                            class="el-icon-switch-button"
                            style="padding: 0px 10px; margin-left: 10px"
                        ></i>
                        <span>退出登录</span>
                    </p>
                </div>
                <!-- <p slot="reference" class="user-info-tool">
                    <span style="margin-left: 0">{{ userInfo.realName }}</span>
                    <img
                        :src="userInfo.headImg ? userInfo.headImg : userImg"
                        style="cursor: pointer"
                    />
                </p> -->
                <template #reference>
                    <div class="reference-btn" :style="{ height: layoutStore.header.height }">
                        <span class="user-name">{{ userInfo.realName }}</span>
                        <el-avatar
                            :src="userInfo.headImg ? userInfo.headImg : userImg"
                            :size="30"
                        />
                    </div>
                </template>
            </el-popover>
        </div>
    </div>
</template>

<script>
import wendang from '@/const/img/文档.png';
import renwu from '@/const/img/任务.png';
import { relativelyTime } from '@/util/date';
import logo from '@/views/common/img/logo.png';
import user from '@/views/common/img/user.png';
import pc from '@/views/common/img/pc.png';
import out from '@/views/common/img/out.png';
import { getStore } from '@/util/store.js';
import { loginoutHandle } from '@/api/login';
import { getDynamicResource } from '@/api/common';
import eventBus from '@/util/eventBus';
import stepForm from '@/components/basic-assembly/stepForm.vue';
import { mapState } from 'vuex';
import { getMyTenantList } from '@/api/new_project/user';
import { client_id } from '@/const/const';
import { getMessageDetail, messageAllRead, messageaPage } from '@/api/message';
export default {
    components: { stepForm },
    name: 'top-bar',
    props: {
        inKeyword: {
            type: String,
        },
        fresh: {
            type: Boolean,
        },
    },
    data() {
        return {
            // 查询条件
            queryParams: {
                search: '',
                type: '',
            },
            activeName: '1',
            page: {
                total: 0,
                size: 20,
                current: 1,
            },
            noDetailList: {},
            noDetailLoading: false, // 消息详情弹窗loading
            noList: [],
            mesOption: [
                {
                    name: '1',
                    label: '全部消息',
                },
                {
                    name: '2',
                    label: '未读消息',
                },
            ],
            tabActive: '1',
            connectVisible: false, // 所有消息弹窗标识
            connectVisibles: false, // 消息详情弹窗标识
            messageTitle: '消息详情',
            insideList: [],
            messageLoading: false,
            readStatus: false,
            visible: false,
            linkList: [],
            clientId: client_id,
            wendang: wendang,
            renwu: renwu,
            logo: logo,
            // logoImg: logo,
            userImg: user,
            pcImg: pc,
            outImg: out,
            alreadyLogin: false,
            userInfo: {},
            loading: false,
            readIs: false,
            pageObj: {
                current: 1,
                size: 10,
                total: 0,
            },
            value: '',
            changeTenantsList: [], // 可切换的租户
        };
    },
    mounted() {
        this.connectWebsocket();
    },
    filters: {
        formatLogTime(value) {
            return value; // relativelyTime(value)
        },
    },
    computed: {
        ...mapState({
            systemHelpDict: (state) => state.common.systemHelpDict,
            remainingCount: (state) => state.socket.remainingCount,
        }),
        logoImg() {
            // return store?.state?.common?.tenantInfo?.logo || this.logo
            return getStore({ name: 'userInfo' }).tenant.logo || this.logo;
        },
        /** 系统名称 */
        appTitle() {
            return '通关信用管理系统';
        },
        layoutStore() {
            return useLayoutStoreHook();
        },
    },
    methods: {
        handleCurrentChange(val) {
            this.page.current = val;
            this.getMessagePage();
        },
        getMessagePage() {
            this.messageLoading = true;
            let obj = {
                current: this.page.current,
                size: this.page.size,
            };
            messageaPage(obj)
                .then((res) => {
                    if (res.data.code == 0) {
                        this.insideList = JSON.parse(JSON.stringify(res.data.data.records));
                        this.page.total = res.data.data.total;
                        this.insideList.forEach((item) => {
                            item.msgContent = JSON.parse(item.msgContent);
                        });
                    }
                })
                .finally((res) => {
                    this.messageLoading = false;
                });
        },
        // 打开消息通知
        openMessage() {
            this.activeName = '1';
            this.getMessage();
        },
        tabClick(e) {
            if (e.name == '1') {
                this.getMessage('notice');
            } else {
                this.getMessage('project');
            }
        },
        connectWebsocket() {
            this.$store.dispatch('MESSAGE_WS_INIT', this.userInfo.id);
        },
        // 关闭弹窗
        handleConnectClose() {
            this.connectVisible = false;
        },
        // 关闭弹窗
        handleConnectCloses() {
            this.connectVisibles = false;
        },
        // 所有消息
        allnotice() {
            // this.connectVisible = true
            this.messageTitle = '所有消息';
            this.connectVisibles = true;
            this.noDetailList = {};
            this.visible = false;
            //查询所有
            this.getMessagePage();
        },
        //标记全部已读
        editreadStatus() {
            this.readStatus = true;
            messageAllRead().then((res) => {
                if (res.data.code == 0) {
                    this.$store.commit('SET_MESSAGE_DATA', 0);
                }
            });
        },
        selectDetail(id, index, params, data) {
            this.noDetailLoading = true;
            getMessageDetail(id)
                .then((res) => {
                    if (res.data && res.data.code == 0) {
                        this.noDetailList = JSON.parse(JSON.stringify(res.data.data));
                        this.noDetailList.msgContent = JSON.parse(res.data.data.msgContent);
                        this.visible = false;
                        if (!this[params][index].readIs) {
                            this[params][index].readIs = true;
                            this.$store.commit('SET_MESSAGE_DATA', this.remainingCount - 1);
                        }
                    }
                    this.noDetailLoading = false;
                })
                .catch((err) => {
                    this.noDetailLoading = false;
                });
        },
        // 消息详情
        changeDetail(id, index, params, data) {
            this.messageTitle = '消息详情';
            this.connectVisibles = true;
            this.noDetailList = {};
            this.selectDetail(id, index, params, data);
        },
        // 消息中心
        getMessage(type) {
            // this.$router.push({path: '/message'})
            this.readStatus = false;
            this.messageLoading = true;
            let obj = {
                current: 1,
                size: 20,
                largeCategories: type ? type : 'notice',
            };
            if (this.queryParams.search) {
                obj.search = this.queryParams.search;
            }
            if (this.queryParams.type) {
                obj.type = this.queryParams.type;
            }
            messageaPage(
                obj,
                // {current:1, size:20, tabActive:this.tabActive,}
            )
                .then((res) => {
                    if (res.data.code == 0) {
                        this.insideList = JSON.parse(JSON.stringify(res.data.data.records));
                        this.insideList.forEach((item) => {
                            item.msgContent = JSON.parse(item.msgContent);
                        });
                        // console.log(this.insideList)
                    }
                })
                .finally((res) => {
                    this.messageLoading = false;
                });
        },
        // 显示基本设置
        handleShowInfo() {
            this.isShowInfo = true;
        },
        // 隐藏基本设置
        handleHideInfo() {
            this.isShowInfo = false;
        },

        handleTo(path) {
            // store.commit("SET_PAGE_CACHE", {})
            this.$router.push(path);
        },
        goHelp() {
            this.systemHelpDict.forEach((item) => {
                if (item.label === 'event-auto-help') {
                    this.$openUrl(item.value, '_blank');
                }
            });
        },
        goHome() {
            // if (this.alreadyLogin) {
            //     if (this.$route.path != '/dynaIndex') {
            //         this.$router.push({ path: '/dynaIndex' });
            //     }
            // } else {
            //     if (this.$route.path != '/') {
            //         this.$router.push({ path: '/' });
            //     }
            // }
        },
        //个人中心
        goInfo() {
            this.$router.push({ path: '/user' });
        },
        loginout() {
            this.$confirm('是否退出系统, 是否继续?', '提示', {
                confirmButtonText: '确定',
                cancelButtonText: '取消',
                type: 'warning',
            }).then(() => {
                loginoutHandle().then((res) => {
                    if (res.data.code == 0) {
                        sessionStorage.clear();
                        localStorage.clear();
                        if (this.websocket) {
                            this.websocket.close();
                            this.websocket = null;
                        }
                        this.$store.dispatch('LogOut', false);
                        // this.$router.push({ path: '/login' });
                    }
                });
            });
        },
        goLogin() {
            this.$openLogin({
                successClose: false,
                closeable: true,
                hasModal: true,
                afterLogin: (dialog, res) => {
                    console.log('登录提交。。。。。');
                    dialog.handleClose();
                    this.$emit('fresh', true);
                    this.alreadyLogin = true;
                    this.userInfo = getStore({ name: 'userInfo' });
                    if (this.$route.query.toPath) {
                        if (this.$route.query.toPath == '/404') {
                            this.$router.push('/dynaIndex');
                        } else {
                            this.$router.push(this.$route.query.toPath);
                        }
                    } else {
                        this.$router.push('/dynaIndex');
                    }
                },
                afterRegister: () => {
                    console.log('注册提交。。。。。');
                },
            });
        }, // 我的组织
        getMyTenantListHandle() {
            getMyTenantList().then((res) => {
                if (res.data.code == 0) {
                    this.myTenantList = res.data.data;
                    this.getChangeTenants();
                }
            });
        },
        // 获取切换的租户列表
        getChangeTenants() {
            let temp = [];
            // temp = [...this.myTenantList]
            temp = [...this.$store.getters.userInfo.tenants];
            this.changeTenantsList = temp.filter((ti) => {
                return ti.id != this.$store.getters.userInfo.tenantId;
            });
        },
        // 切换租户
        switchTenant() {
            this.$openLogin({
                successClose: false,
                hasModal: true,
                switchTenant: true,
                switchList: this.changeTenantsList,
                afterLogin: (dialog, res) => {
                    dialog.handleClose();
                    location.reload();
                },
            });
        },
        getDynamicResource() {
            getDynamicResource().then((res) => {
                if (res.data && res.data.code == 0) {
                    this.linkList = res.data.data;
                }
            });
        },
        enterLink(item) {
            this.$openUrl(`${item.url}`, '_blank');
        },
    },
    created() {
        if (getStore({ name: 'userInfo' })) {
            this.alreadyLogin = true;
            this.userInfo = getStore({ name: 'userInfo' });
        } else {
            this.alreadyLogin = false;
            this.userInfo = {};
        }
        this.getDynamicResource();
        this.getMyTenantListHandle();
        eventBus.$off('freshUserInfo');
        eventBus.$on('freshUserInfo', (data) => {
            if (data) {
                this.userInfo = data;
                this.$forceUpdate();
            }
        });
        eventBus.$on('loginEvent', (type) => {
            switch (type) {
                case 'loginOut':
                    sessionStorage.clear();
                    localStorage.clear();
                    this.alreadyLogin = false;
                    this.userInfo = {};
                    this.$forceUpdate();
                    break;
                default:
                    break;
            }
        });
    },
    beforeDestory() {
        eventBus.$off('freshUserInfo');
    },
    watch: {
        inKeyword(newVal, oldVal) {
            if (!newVal) {
                this.keyword = '';
            }
        },
        fresh: {
            handler(newVal, oldVal) {
                if (newVal) {
                    this.alreadyLogin = true;
                    this.userInfo = getStore({ name: 'userInfo' });
                } else {
                    this.alreadyLogin = false;
                }
                console.log(newVal);
            },
        },
    },
};
</script>
<style lang="scss" scoped>
::v-deep .el-dialog__body {
    padding: 0 20px 30px !important;
}
.risk-policy-top-bar {
    width: 100%;
    display: flex;
    justify-content: space-between;
    align-items: center;
    height: 60px;
    padding: 0 20px;
    box-sizing: border-box;
    position: relative;
    .left {
        height: 100%;
        display: flex;
        justify-content: center;
        align-items: center;
        img {
            height: 50px;
            // height: 40px;
            display: block;
            cursor: pointer;
        }
        .title {
            margin-left: 10px;
            .title-cn {
                font-size: 16px;
            }
            .title-en {
                font-size: 14px;
                color: #fff;
            }
        }
        .system-name {
            font-size: 18px;
        }
        .home-btn {
            display: inline-flex;
            // font-family: MiSans-Demibold;
            margin-left: 40px;
            align-items: center;
            .btn-item {
                font-size: 16px;
                margin: 0px 20px;
                color: #101010;
                cursor: pointer;
                &:hover {
                    color: #3471ff;
                }
            }
            .btn-line {
                height: 24px;
                background-color: rgb(215, 215, 215);
                width: 1px;
            }
            .item-active {
                color: #3471ff;
            }
        }
    }
    .center {
        align-items: center;
        display: flex;
        flex: 1;
        .el-input {
            width: 460px;
            height: 40px;
            .el-input__inner {
                height: 40px;
                line-height: 40px;
                border-radius: 30px;
                background: #f3f3f3;
                font-size: 14px;
                padding-left: 40px;
                border-color: #f3f3f3;
                outline: none;
            }
            .el-input__prefix {
                left: 15px;
                top: 2px;
                i {
                    font-size: 20px;
                    // line-height: 60px;
                }
            }
        }
    }
    .right {
        display: flex;
        align-items: center;
        position: absolute;
        right: 0px;
        // padding-right: 50px;
        padding-right: 20px;
        fill: currentColor;
        .help-center {
            color: #a4a0a0;
            cursor: pointer;
            margin-left: 20px;
            font-size: 16px;
        }
        .right-search {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            // background-color: #F7eeF1;
            display: flex;
            align-items: center;
            justify-content: center;
            // cursor: pointer;
            i {
                font-size: 24px;
                color: rgba(183, 182, 182, 100);
            }
        }
        a {
            font-size: 18px;
            font-family: MiSans-Demibold;
            font-weight: 400;
            line-height: 31px;
            color: #333333;
            cursor: pointer;
        }
        .el-button {
            margin-left: 10px;
            background: #3471ff;
            font-size: 16px;
            width: 88px;
            height: 30px;
            line-height: 30px;
            padding: 0;
            span {
                display: block;
            }
        }
        .user-info-tool {
            margin: 0 0 0 10px;
            color: rgb(51, 51, 51);
            // cursor: pointer;
            display: flex;
            align-items: center;
            span {
                font-size: 16px;
            }
            img {
                display: block;
                width: 25px;
                height: 25px;
                margin-left: 10px;
                border-radius: 50%;
                overflow: hidden;
            }
        }
        // img{
        //   display: block;
        //   width: 28px;
        //   height: 28px;
        //   margin-left: 20px;
        //   border-radius: 50%;
        //   overflow: hidden;
        // }
    }
}
.top-tool-bar {
    // padding: 15px 0;
    p {
        cursor: pointer;
        display: flex;
        align-items: center;
        //padding: 0 12px;
        margin: 0;
        line-height: 36px;
        img {
            display: block;
            width: 14px;
            //height: 18px;
            // margin-right: 20px;
            padding: 0 20px;
        }
        span {
            font-size: 14px;
            color: #606266;
            font-family: MiSans-Demibold;
        }
    }
}
.top-tool-bar p:hover {
    background: #f5f7fa;
}
.el-popper[x-placement^='right'],
.el-popper[x-placement^='bottom'] {
    padding: 0 !important;
}
.el-popover.my-popover {
    height: 677px;
    padding: 0px !important;
    .bottom-txt {
        position: relative;
        bottom: 0px;
        .bottom-all {
            //color: #101010;
            font-size: 14px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 16px 30px;
            color: #a2a3a5;
            cursor: pointer;
        }
        .bottom-all:hover {
            //color: #3471ff;
            background-color: #f2f6f8;
            cursor: pointer;
        }
    }
    .right-tab {
        height: 30px;
        display: flex;
        padding: 25px 29px 0px 29px;
        .select-options {
            width: 120px;
            height: 25px;
        }
        .right-tabsize {
            position: absolute;
            right: 29px;
            margin-top: -7px;
            cursor: pointer;
            .right-tabhover {
                // color: #3471ff;
            }
            .right-tabhover:hover {
                color: #3471ff;
            }
        }
    }
    .el-divider--horizontal {
        margin: 0 auto;
    }
}
.my-popover-box * {
    box-sizing: initial !important;
}
.message-detail {
    display: flex;
    justify-content: space-between;
    .left {
        width: 380px;
        .left-pagination {
            padding: 12px 0;
            text-align: right;
        }
    }
    .right {
        width: calc(100% - 400px);
        position: relative;
        .loading-back {
            background-image: url('/public/jvs-ui-public/img/loading.gif');
            background-color: #fff;
            background-repeat: no-repeat;
            background-position: center;
            //background-size: 200px 160px;
            position: absolute;
            width: 100%;
            height: 100%;
            top: 0;
            left: 0;
            z-index: 999999;
        }
        .right-title {
            font-size: 24px;
            color: #333333;
            margin-bottom: 20px;
        }
    }
}
.top-tab:hover {
    color: #3471ff;
    background-color: #f2f6f8;
    .top-tab-span {
        // color: #3471ff;
    }
}
.top-tab {
    padding: 8px 10px;
    cursor: pointer;
    margin: 0 20px;
    border-radius: 6px;
    position: relative;
    .top-tab-span {
        width: 158px;
        overflow: hidden; //超出的文本隐藏
        text-overflow: ellipsis; //溢出用省略号显示
        white-space: nowrap; //溢出不换行
        font-size: 16px;
        font-weight: normal;
        // color: #000;
    }
    .top-tab-span-span {
        position: absolute;
        right: 36px;
        //line-height: 60px;
        // color: #101010;
        bottom: 10px;
        font-size: 12px;
    }
    .top-txt {
        padding: 10px 0 5px 0px;
        .top-tab-span-span {
            position: absolute;
            right: 55px;
            // color: #101010;
            font-size: 14px;
        }
    }
    .el-divider--horizontal {
        margin: 10px auto 0px !important;
    }
    .container-page {
        text-align: right;
        padding: 10px 0px 0px;
        position: absolute;
        bottom: 55px;
        right: 36px;
    }
}
.customheight {
    height: 680px;
    .right-tab {
        height: 30px;
        display: flex;
        //padding: 25px 29px 0px 29px;
        .select-options {
            width: 120px;
            height: 25px;
        }
        .right-tabsize {
            position: absolute;
            right: 40px;
            margin-top: -7px;
            cursor: pointer;
            .right-tabhover {
                // color: #3471ff;
            }
            .right-tabhover:hover {
                color: #3471ff;
            }
        }
    }
    .top-tab-span {
        width: 800px;
    }
    .top-tabs {
        padding: 26px 0px 0px 40px;
        //height: 100%;
        .top-tabs-pad {
            line-height: 60px;
            width: 900px;
            font-size: 16px;
            font-weight: normal;
        }
        .top-tabs-pad-content {
            //height: 200px;
            //overflow-y: auto;
        }
    }
}
</style>
<style scoped lang="scss">
//消息
.bottom-txt {
    position: relative;
    bottom: 0px;
    .bottom-all {
        color: #101010;
        font-size: 16px;
        padding: 16px 179px;
        cursor: pointer;
    }
    .bottom-all:hover {
        color: #3da8f5;
        background-color: #f2f6f8;
        font-size: 16px;
        padding: 16px 179px;
        cursor: pointer;
    }
}
//弹框
.task-headers {
    padding: 10px 0px 10px 430px;
    border-bottom: 1px solid #e5e5e5;
    display: flex;
    vertical-align: middle;
    margin-top: -30px;
    margin-left: -20px;
    margin-right: -20px;
    background-color: #f2f6f8;
    .head-title {
        padding: 0 10px 0 40px;
        flex: 1 1;
        font-size: 20px;
    }
    .header-actions {
        font-size: 16px;
        padding: 0 20px;
        display: flex;
        max-height: 24px;
        .action-item {
            margin-left: 10px;
            padding: 4px;
            transition: 218ms;
            transition-property: background, color;
            border-radius: 4px;
            align-items: center;
            display: flex;
            text-align: center;
            justify-content: center;
            min-width: 32px;
            cursor: pointer;
            span {
                margin-left: 6px;
                font-size: 14px;
            }
            &.active {
                color: #3da8f5;
            }

            &:hover {
                // background: #ecf6fe;
                // color: #3da8f5;
                // border-radius: 4px;
            }
        }
    }
    &.disabled {
        background: #f5f5f5;
    }
}
::v-deep .el-dialog__header {
    display: none !important;
}
.setting-icon {
    padding: 10px;
    //margin: 0 10px;
    //font-size: 18px;
    //width: 20px;
    border-radius: 4px;
    transition: 0.2s;
    cursor: pointer;
    &:hover {
        transition: 0.2s;
        background-color: #eee;
    }
}
.link-list-box {
    display: flex;
    flex-wrap: wrap;
    padding: 12px;
    .link-list-item {
        box-sizing: border-box;
        min-height: 106px;
        padding: 12px 2px;
        position: relative;
        width: 33.33333%;
        cursor: pointer;
        border-radius: 5px;
        overflow: hidden;
        div {
            display: flex;
            flex-direction: column;
            align-items: center;
            border-radius: 4px;
            box-sizing: border-box;
            cursor: pointer;
            overflow: hidden;
            padding: 8px 3px;
            text-align: center;
            img {
                width: 40px;
                height: 40px;
                display: inline-block;
            }
            span {
                margin-top: 4px;
                box-sizing: border-box;
                display: inline-block;
                font-size: 14px;
                height: 22px;
                line-height: 22px;
                max-width: 100%;
                overflow: hidden;
                text-overflow: ellipsis;
                white-space: nowrap;
            }
        }
    }
    .link-list-item:hover {
        background: #f5f7fa;
    }
}
</style>

<!-- 新加的样式 -->
<style scoped lang="scss">
.reference-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    .user-name {
        margin-right: 10px;
    }
}
.user-popover {
    .el-button {
        width: 100%;
        height: 40px !important;
        + .el-button {
            margin-left: 0 !important;
        }
    }
}
</style>
