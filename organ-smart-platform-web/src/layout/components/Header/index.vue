<script setup lang="ts">
import User from "./User.vue";
import { useTabsStoreHook } from "@/store/modules/tabs";

/**
 * Header组件
 * 显示系统标题、当前页面标题和用户信息
 */
const layoutStore = useLayoutStoreHook();
const tabsStore = useTabsStoreHook();
const route = useRoute();

// 当前页面标题
const currentPageTitle = computed(() => {
  // 从当前激活的标签页获取标题
  const currentTag = tabsStore.multiTags.find((tag) => {
    if (Object.keys(route.query).length > 0) {
      return JSON.stringify(route.query) === JSON.stringify(tag.query);
    } else if (Object.keys(route.params).length > 0) {
      return JSON.stringify(route.params) === JSON.stringify(tag.params);
    } else {
      return route.path === tag.path;
    }
  });

  return currentTag?.meta?.title || route.meta?.title || "机关事务管理系统";
});
</script>

<template>
  <header
    class="header-container"
    :style="{
      height: layoutStore.header.height,
      color: layoutStore.header.textColor,
      backgroundColor: layoutStore.header.backgroundColor,
      borderBottom: layoutStore.header.borderBottom,
      marginLeft: layoutStore.getCurrentAsideWidth,
    }"
  >
    <!-- 左侧：当前页面标题 -->
    <div class="header-left">
      <div class="page-title">
        {{ currentPageTitle }}
      </div>
    </div>

    <!-- 右侧：用户信息 -->
    <div class="header-right">
      <User />
    </div>
  </header>
</template>

<style scoped lang="less">
.header-container {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  box-sizing: border-box;
  padding: 0 20px;
  display: flex;
  align-items: center;
  justify-content: space-between;
  z-index: 1001;
  transition: margin-left 0.3s ease;

  .header-left {
    flex: 1;
    display: flex;
    align-items: center;

    .page-title {
      font-size: 16px;
      font-weight: 500;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      color: rgba(0, 0, 0, 0.85);
    }
  }

  .header-right {
    flex-shrink: 0;
    display: flex;
    align-items: center;
  }
}
</style>
