<script setup lang="ts">
import { toLogin } from "@/util/login";
import { useTabsStoreHook } from "@/store/modules/tabs";

/**
 * 用户信息组件
 * 显示用户头像、姓名，提供用户操作菜单
 */
const layoutStore = useLayoutStoreHook();
const userStore = useUserStoreHook();
const tabsStore = useTabsStoreHook();
const router = useRouter();

// 用户信息
const userInfo = computed(() => {
  return {
    name: userStore.user.name || userStore.userInfo.name || "用户",
    avatar: userStore.user.avatar || userStore.userInfo.avatar || "",
  };
});

/**
 * 处理退出登录
 */
const handleLogout = async () => {
  try {
    // 确认退出
    await ElMessageBox.confirm("确定要退出登录吗？", "退出确认", {
      confirmButtonText: "确定",
      cancelButtonText: "取消",
      type: "warning",
    });

    // 清理用户信息
    userStore.clearToken();
    userStore.clearAccessToken();

    // 清理标签页
    tabsStore.closeAllTabs();

    // 跳转到登录页
    toLogin();

    ElMessage.success("退出登录成功");
  } catch (error) {
    // 用户取消退出
    console.log("用户取消退出登录");
  }
};

/**
 * 处理修改资料
 */
const handleEditProfile = () => {
  ElMessage.info("修改资料功能开发中...");
};
</script>

<template>
  <el-popover
    placement="bottom-end"
    width="140"
    trigger="click"
    :hide-after="10"
  >
    <template #reference>
      <div class="reference-btn" :style="{ height: layoutStore.header.height }">
        <span class="user-name">{{ userInfo.name }}</span>
        <el-avatar :src="userInfo.avatar" :size="32" class="user-avatar">
          <el-icon><User /></el-icon>
        </el-avatar>
      </div>
    </template>

    <div class="user-popover">
      <div class="user-info">
        <el-avatar :src="userInfo.avatar" :size="40" class="popover-avatar">
          <el-icon><User /></el-icon>
        </el-avatar>
        <div class="user-details">
          <div class="user-name-text">{{ userInfo.name }}</div>
          <div class="user-role">管理员</div>
        </div>
      </div>

      <el-divider style="margin: 12px 0" />

      <div class="user-actions">
        <el-button text class="action-btn" @click="handleEditProfile">
          <el-icon><Edit /></el-icon>
          <span>修改资料</span>
        </el-button>

        <el-button text class="action-btn logout-btn" @click="handleLogout">
          <el-icon><SwitchButton /></el-icon>
          <span>退出登录</span>
        </el-button>
      </div>
    </div>
  </el-popover>
</template>

<style scoped lang="less">
.reference-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  padding: 0 12px;
  border-radius: 4px;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: rgba(0, 0, 0, 0.04);
  }

  .user-name {
    margin-right: 8px;
    font-size: 14px;
    font-weight: 400;
    color: rgba(0, 0, 0, 0.85);
  }

  .user-avatar {
    border: none;
  }
}

.user-popover {
  .user-info {
    display: flex;
    align-items: center;
    padding: 8px 0;

    .popover-avatar {
      margin-right: 12px;
    }

    .user-details {
      flex: 1;

      .user-name-text {
        font-size: 14px;
        font-weight: 600;
        color: #333;
        margin-bottom: 2px;
      }

      .user-role {
        font-size: 12px;
        color: #666;
      }
    }
  }

  .user-actions {
    .action-btn {
      width: 100%;
      height: 36px !important;
      justify-content: flex-start;
      padding: 0 8px;
      margin: 0 0 4px 0 !important;
      border-radius: 4px;

      .el-icon {
        margin-right: 8px;
        font-size: 16px;
      }

      span {
        font-size: 13px;
      }

      &:hover {
        background-color: #f5f7fa;
      }

      &.logout-btn {
        color: #f56c6c;

        &:hover {
          background-color: #fef0f0;
        }
      }
    }
  }
}
</style>
