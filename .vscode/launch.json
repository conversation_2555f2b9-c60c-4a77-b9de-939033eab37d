{
  // Use IntelliSense to learn about possible attributes.
  // Hover to view descriptions of existing attributes.
  // For more information, visit: https://go.microsoft.com/fwlink/?linkid=830387
  "version": "0.2.0",
  "configurations": [
    {
      "type": "java",
      "name": "CanteenApplication",
      "request": "launch",
      "mainClass": "com.organ.module.CanteenApplication",
      "projectName": "organ-canteen-biz",
      "cwd": "${workspaceFolder}/organ-smart-platform/organ-canteen/organ-canteen-biz",
      // "classPaths": [
      //   "${workspaceFolder}/organ-smart-platform/organ-canteen/organ-canteen-biz/target/classes",
      //   "${workspaceFolder}/organ-smart-platform/organ-canteen/organ-canteen-api/target/classes"
      // ],
      "modulePaths": [],
      "vmArgs": [
        "-Dspring.profiles.active=dev",
        "-Dfile.encoding=UTF-8",
        "-Djava.security.egd=file:/dev/./urandom",
        "-Xms512m",
        "-Xmx1024m"
      ],
      "args": [],
      "env": {
        "CUR_ENV": "dev"
      },
      "console": "integratedTerminal",
      "stopOnEntry": false,
      "stepFilters": {
        "skipClasses": [],
        "skipSynthetics": false,
        "skipStaticInitializers": false,
        "skipConstructors": false
      }
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "Start FE PC",
      "url": "http://localhost:9599",
      "webRoot": "${workspaceFolder}/organ-smart-platform-web",
      "sourceMaps": true,
      "sourceMapPathOverrides": {
        "webpack:///src/*": "${webRoot}/src/*",
        "webpack:///./*": "${webRoot}/*",
        "webpack:///./~/*": "${webRoot}/node_modules/*",
        "webpack://?:*/*": "${webRoot}/*"
      },
      "skipFiles": ["node_modules/**", "<node_internals>/**"],
      "pathMapping": {
        "/": "${workspaceFolder}/organ-smart-platform-web/src"
      },
      "preLaunchTask": "Pre-launch FE PC"
    },
    {
      "type": "chrome",
      "request": "launch",
      "name": "Start FE APP",
      "url": "http://localhost:9102",
      "webRoot": "${workspaceFolder}/organ-smart-platform-app",
      "sourceMaps": true,
      "sourceMapPathOverrides": {
        "webpack:///src/*": "${webRoot}/src/*",
        "webpack:///./*": "${webRoot}/*",
        "webpack:///./~/*": "${webRoot}/node_modules/*",
        "webpack://?:*/*": "${webRoot}/*"
      },
      "skipFiles": ["node_modules/**", "<node_internals>/**"],
      "pathMapping": {
        "/": "${workspaceFolder}/organ-smart-platform-app/src"
      },
      "preLaunchTask": "Pre-launch FE Mobile"
    }
  ],
  "compounds": [
    {
      "name": "Server & FE PC",
      "configurations": ["CanteenApplication", "Start FE PC"],
      "stopAll": false
    },
    {
      "name": "Server & FE APP",
      "configurations": ["CanteenApplication", "Start FE APP"],
      "stopAll": false
    }
  ]
}
