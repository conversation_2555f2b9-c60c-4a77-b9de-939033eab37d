/**
 * 环境变量类型定义
 * 为 process.env 提供完整的类型提示和智能补全
 */

/**
 * 应用运行环境枚举
 */
export type AppEnv = 'DEV' | 'PROD'

/**
 * Node.js 环境枚举
 */
export type NodeEnv = 'development' | 'production'

/**
 * 环境变量接口定义
 */
interface _ProcessEnv {
  /**
   * Node.js 运行环境
   * @description 构建环境变量，决定应用的构建模式
   * @example 'development' | 'production'
   */
  readonly NODE_ENV: NodeEnv

  /**
   * 应用名称
   * @description 应用的显示名称，用于页面标题等
   * @example '求职报告'
   */
  readonly VUE_APP_NAME: string

  /**
   * 应用运行环境
   * @description 应用的运行环境标识，用于区分不同部署环境
   * @example 'DEV' | 'PROD'
   */
  readonly VUE_APP_ENV: AppEnv

  /**
   * API 基础路径
   * @description 后端 API 的基础路径
   * @example '/api'
   */
  readonly VUE_APP_BASE_API: string

  /**
   * 静态资源地址
   * @description 公共静态资源的 CDN 地址
   * @example 'https://app.hainancrc.com/front-static/multi/prod'
   */
  readonly VUE_APP_STATIC_IMG_URL: string
}

/**
 * 扩展全局 NodeJS 命名空间
 * 为 process.env 提供类型定义
 */
declare global {
  namespace NodeJS {
    interface ProcessEnv extends ProcessEnv {}
  }
}

/**
 * 环境变量工具函数类型定义
 */
export interface EnvUtils {
  /**
   * 判断是否为开发环境
   */
  isDev: boolean

  /**
   * 判断是否为生产环境
   */
  isProd: boolean

  /**
   * 获取应用名称
   */
  getAppName: () => string

  /**
   * 获取 API 基础路径
   */
  getApiBaseUrl: () => string

  /**
   * 获取静态资源地址
   */
  getStaticUrl: () => string
}

export {}
