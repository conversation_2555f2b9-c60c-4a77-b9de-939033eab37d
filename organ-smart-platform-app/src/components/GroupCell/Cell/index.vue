<template>
  <div
    class="cell-container"
    :class="{ 'no-border': !showBorder }"
    @click="handleClick"
  >
    <!-- 左侧内容区域 -->
    <div class="cell-left">
      <!-- 左侧图标 -->
      <div v-if="leftIcon" class="cell-icon left-icon">
        <i :class="leftIcon"></i>
      </div>
      <!-- 左侧插槽 -->
      <div class="cell-left-content">
        <slot name="left">
          <span v-if="title" class="cell-title">{{ title }}</span>
        </slot>
      </div>
    </div>

    <!-- 右侧内容区域 -->
    <div class="cell-right">
      <!-- 右侧插槽 -->
      <div class="cell-right-content">
        <slot name="right">
          <span v-if="value" class="cell-value">{{ value }}</span>
        </slot>
      </div>

      <!-- 角标 -->
      <div v-if="badge !== null && badge !== undefined" class="cell-badge">
        <span class="badge-text">{{ badge }}</span>
      </div>

      <!-- 右侧图标 -->
      <div v-if="rightIcon" class="cell-icon right-icon">
        <i :class="rightIcon"></i>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { withDefaults } from "vue";

/**
 * Cell组件的Props接口定义
 */
interface Props {
  /** 左侧图标类名 */
  leftIcon?: string;
  /** 右侧图标类名 */
  rightIcon?: string;
  /** 标题文本（左侧默认内容） */
  title?: string;
  /** 值文本（右侧默认内容） */
  value?: string;
  /** 角标内容（数字或文本） */
  badge?: string | number | null;
  /** 是否显示下边框 */
  showBorder?: boolean;
}

/**
 * 事件定义
 */
interface Emits {
  (e: "click", event: Event): void;
}

// Props定义和默认值
const props = withDefaults(defineProps<Props>(), {
  leftIcon: "",
  rightIcon: "el-icon-arrow-right",
  title: "",
  value: "",
  badge: null,
  showBorder: true,
});

// 事件定义
const emit = defineEmits<Emits>();

/**
 * 点击事件处理
 * @param event 点击事件对象
 */
const handleClick = (event: Event): void => {
  emit("click", event);
};
</script>

<style lang="scss" scoped>
.cell-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background-color: #ffffff;
  border-bottom: 1px solid #f0f0f0;
  cursor: pointer;
  transition: background-color 0.2s ease;

  &:hover {
    background-color: #f8f9fa;
  }

  &:active {
    background-color: #f0f1f2;
  }

  // 无边框样式
  &.no-border {
    border-bottom: none;
  }
}

.cell-left {
  display: flex;
  align-items: center;
  flex: 1;
  min-width: 0; // 防止内容溢出

  .left-icon {
    margin-right: 12px;
  }

  .cell-left-content {
    flex: 1;
    min-width: 0;
  }

  .cell-title {
    font-size: 16px;
    color: #333333;
    font-weight: 400;
    line-height: 1.4;
    // 文本溢出处理
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
}

.cell-right {
  display: flex;
  align-items: center;
  position: relative;
  margin-left: 12px;

  .cell-right-content {
    margin-right: 8px;
  }

  .cell-value {
    font-size: 14px;
    color: #666666;
    line-height: 1.4;
  }

  .right-icon {
    margin-left: 8px;
  }
}

.cell-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;

  i {
    font-size: 16px;
    color: #666666;
  }
}

.cell-badge {
  position: relative;
  margin-right: 8px;

  .badge-text {
    display: inline-block;
    min-width: 18px;
    height: 18px;
    padding: 0 6px;
    font-size: 12px;
    line-height: 18px;
    text-align: center;
    color: #ffffff;
    background-color: #ff4757;
    border-radius: 9px;
    font-weight: 500;

    // 单个数字时保持圆形
    &:empty::before {
      content: "";
    }
  }
}

// 响应式设计
@media (max-width: 768px) {
  .cell-container {
    padding: 14px 16px;
  }

  .cell-title {
    font-size: 15px;
  }

  .cell-value {
    font-size: 13px;
  }
}
</style>
