<template>
  <div class="group-cell-container">
    <div class="group-cell-wrapper">
      <!-- 渲染所有Cell组件 -->
      <Cell
        v-for="(cell, index) in cellList"
        :key="index"
        :left-icon="cell.leftIcon"
        :right-icon="cell.rightIcon"
        :title="cell.title"
        :value="cell.value"
        :badge="cell.badge"
        :show-border="shouldShowBorder(index)"
        @click="handleCellClick(cell, index)"
      >
        <!-- 传递左侧插槽内容 -->
        <template v-slot:left>
          <slot :name="`left-${index}`" :cell="cell" :index="index">
            <span v-if="cell.title" class="cell-title">{{ cell.title }}</span>
          </slot>
        </template>

        <!-- 传递右侧插槽内容 -->
        <template v-slot:right>
          <slot :name="`right-${index}`" :cell="cell" :index="index">
            <span v-if="cell.value" class="cell-value">{{ cell.value }}</span>
          </slot>
        </template>
      </Cell>
    </div>
  </div>
</template>

<script setup lang="ts">
import { computed, withDefaults } from "vue";
import Cell from "./Cell/index.vue";
import { CellClickPayload, CellData } from "./types";

/**
 * GroupCell组件的Props接口定义
 */
interface Props {
  /** Cell数据列表 */
  cellList?: CellData[];
  /** 最后一个Cell是否显示下边框 */
  lastCellBorder?: boolean;
  /** 是否显示阴影 */
  showShadow?: boolean;
  /** 圆角大小 */
  borderRadius?: string;
}

/**
 * 事件定义
 */
interface Emits {
  (e: "cell-click", payload: CellClickPayload): void;
}

// Props定义和默认值
const props = withDefaults(defineProps<Props>(), {
  cellList: () => [],
  lastCellBorder: false,
  showShadow: true,
  borderRadius: "8px",
});

// 事件定义
const emit = defineEmits<Emits>();

/**
 * 判断是否应该显示边框
 * @param index 当前Cell的索引
 * @returns 是否显示边框
 */
const shouldShowBorder = (index: number): boolean => {
  // 如果是最后一个Cell，根据lastCellBorder属性决定
  if (index === props.cellList.length - 1) {
    return props.lastCellBorder;
  }
  // 其他Cell都显示边框
  return true;
};

/**
 * Cell点击事件处理
 * @param cell 被点击的Cell数据
 * @param index Cell的索引
 */
const handleCellClick = (cell: CellData, index: number): void => {
  emit("cell-click", { cell, index });
};

// 计算属性用于动态样式
const shadowStyle = computed(() => {
  return props.showShadow ? "0 2px 8px rgba(0, 0, 0, 0.1)" : "none";
});

const hoverShadowStyle = computed(() => {
  return props.showShadow ? "0 4px 12px rgba(0, 0, 0, 0.15)" : "none";
});
</script>

<style lang="scss" scoped>
.group-cell-container {
  margin: 16px;
}

.group-cell-wrapper {
  background-color: #ffffff;
  border-radius: v-bind(borderRadius);
  overflow: hidden;
  box-shadow: v-bind(shadowStyle);
  border: 1px solid #f0f0f0;
  transition: box-shadow 0.3s ease;

  &:hover {
    box-shadow: v-bind(hoverShadowStyle);
  }
}

// 移动端适配
@media (max-width: 768px) {
  .group-cell-container {
    margin: 12px;
  }
}
</style>
