<template>
  <div class="tabs-wrapper">
    <div
      v-for="tab in tabs"
      :key="tab.url"
      class="tab-item"
      :class="{ active: tab.isActive() }"
      @click="handleClickTab(tab)"
    >
      <i :class="tab.icon" class="icon"></i>
      <span class="label">{{ tab.label }}</span>
    </div>
  </div>
</template>

<script setup lang="ts">
/** 当前路由 */
const route = useRoute();
/** 当前路由对象 */
const router = useRouter();

/** TabBar 列表 */
const tabs = computed(() => {
  return [
    {
      label: "首页",
      url: "/client/home",
      icon: "el-icon-house",
      isActive: () => {
        return route.path.startsWith("/client/home");
      },
    },
    {
      label: "就餐码",
      url: "/client/diningCode",
      icon: "el-icon-bank-card",
      isActive: () => {
        return route.path.startsWith("/operations/scanQrCode");
      },
    },
    {
      label: "个人中心",
      url: "/client/personalCenter",
      icon: "el-icon-user",
      isActive: () => {
        return route.path.startsWith("/client/personalCenter");
      },
    },
  ];
});

/** 点击对应的 Tab */
const handleClickTab = (tab: TabBarItem) => {
  if (tab.isActive()) {
    return;
  }
  router.push(`${tab.url}`);
};
</script>

<style scoped lang="scss">
.tabs-wrapper {
  display: flex;
  justify-content: space-around;
  align-items: center;
  height: var(--tabbar-height);
  background-color: #ffffff;
  border-top: 1px solid #f1f1f1;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 1000;

  .tab-item {
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    flex: 1;
    cursor: pointer;

    &.active {
      color: var(--color-primary);
    }

    .label {
      font-size: 14px;
    }

    .icon {
      font-size: 24px;
    }
  }
}
</style>
