<template>
  <div class="meal-card">
    <!-- 餐次名称和状态圆点 -->
    <div class="meal-header">
      <span class="meal-name">{{ mealData.name }}</span>
    </div>

    <!-- 状态标签（如果有） -->
    <div v-if="mealData.statusTag" class="meal-status-tag">
      <StatusTag
        :text="mealData.statusTag.text"
        :type="mealData.statusTag.type"
      />
    </div>

    <!-- 剩余席位 -->
    <div class="meal-remaining">{{ mealData.remaining }}</div>

    <!-- 用餐时间 -->
    <div class="meal-time">{{ mealData.time }}</div>
  </div>
</template>

<script setup lang="ts">
import StatusTag from "./statusTag.vue";
defineProps<{
  // TODO: 需要确认一下数据格式
  mealData: any;
}>();
</script>

<style scoped lang="scss">
.meal-card {
  .meal-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 4px;
    margin-bottom: 4px;

    .meal-name {
      font-size: 14px;
      font-weight: 500;
      color: #333333;
      transition: color 0.2s ease;
    }
  }

  .status-dot {
    width: 6px;
    height: 6px;
    border-radius: 50%;
    display: inline-block;

    &.status-available {
      background-color: #52c41a;
    }

    &.status-partial {
      background-color: #fa8c16;
    }

    &.status-full {
      background-color: #ff4d4f;
    }

    &.status-closed {
      background-color: #8c8c8c;
    }
  }

  .meal-status-tag {
    margin-bottom: 4px;
  }

  .meal-remaining {
    font-size: 12px;
    color: #666666;
    margin-bottom: 2px;
  }

  .meal-time {
    font-size: 11px;
    color: #999999;
  }
}
</style>
