<template>
  <div class="home-page">
    <!-- 头部背景区域 -->
    <div class="header-section">
      <div class="header-content">
        <h1 class="page-title">跨区就餐预约</h1>
        <p class="page-subtitle">美好时光，从选择开始</p>
      </div>
      <div class="header-illustration">
        <img src="@/assets/images/mobile/canteen.png" alt="食堂插画" />
      </div>
    </div>

    <!-- 食堂列表区域 -->
    <div class="content-section">
      <div class="canteen-list">
        <CanteenCard
          v-for="canteen in canteenList"
          :key="canteen.id"
          :canteen-data="canteen"
          @meal-click="handleMealClick"
          @card-click="handleCardClick"
        />
      </div>
    </div>

    <TabBar />
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import TabBar from "@/components/TabBar/index.vue";
import CanteenCard from "./_comp/CanteenCard.vue";

/**
 * 餐次数据接口
 */
interface MealData {
  type: string;
  name: string;
  status: "available" | "partial" | "full" | "closed";
  statusTag?: {
    text: string;
    type: "available" | "partial" | "full" | "closed";
  };
  remaining: string;
  time: string;
  disabled?: boolean;
}

/**
 * 食堂数据接口
 */
interface CanteenData {
  id: string;
  name: string;
  location: string;
  status: {
    text: string;
    type: "available" | "partial" | "full" | "closed";
  };
  meals: MealData[];
}

// 食堂列表数据
const canteenList = ref<CanteenData[]>([
  {
    id: "1",
    name: "学苑食堂",
    location: "教学楼A区1楼",
    status: {
      text: "可预约",
      type: "available",
    },
    meals: [
      {
        type: "lunch",
        name: "午餐",
        status: "available",
        remaining: "剩余 67 位",
        time: "11:00-13:00",
      },
      {
        type: "dinner",
        name: "晚餐",
        status: "available",
        remaining: "剩余 67 位",
        time: "17:00-19:00",
      },
    ],
  },
  {
    id: "2",
    name: "梧桐食堂",
    location: "教学楼B区2楼",
    status: {
      text: "部分约满",
      type: "partial",
    },
    meals: [
      {
        type: "lunch",
        name: "午餐",
        status: "full",
        statusTag: {
          text: "已约满",
          type: "full",
        },
        remaining: "剩余 2 位",
        time: "11:00-13:00",
      },
      {
        type: "dinner",
        name: "晚餐",
        status: "available",
        remaining: "剩余 67 位",
        time: "17:00-19:00",
      },
    ],
  },
  {
    id: "3",
    name: "紫荆食堂",
    location: "学生活动中心1楼",
    status: {
      text: "可预约",
      type: "available",
    },
    meals: [
      {
        type: "lunch",
        name: "午餐",
        status: "available",
        remaining: "剩余 23 位",
        time: "11:00-13:00",
      },
      {
        type: "dinner",
        name: "晚餐",
        status: "available",
        remaining: "剩余 67 位",
        time: "17:00-19:00",
      },
    ],
  },
  {
    id: "4",
    name: "桂花食堂",
    location: "宿舍区C栋1楼",
    status: {
      text: "预约未开始",
      type: "closed",
    },
    meals: [
      {
        type: "lunch",
        name: "午餐",
        status: "closed",
        statusTag: {
          text: "预约未开始",
          type: "closed",
        },
        remaining: "预约未开始",
        time: "11:00-13:00",
      },
      {
        type: "dinner",
        name: "晚餐",
        status: "closed",
        statusTag: {
          text: "预约未开始",
          type: "closed",
        },
        remaining: "预约未开始",
        time: "17:00-19:00",
      },
    ],
  },
  {
    id: "5",
    name: "银杏食堂",
    location: "图书馆副楼B1",
    status: {
      text: "可预约",
      type: "available",
    },
    meals: [
      {
        type: "lunch",
        name: "午餐",
        status: "available",
        remaining: "剩余 23 位",
        time: "11:00-13:00",
      },
      {
        type: "dinner",
        name: "晚餐",
        status: "available",
        remaining: "剩余 67 位",
        time: "17:00-19:00",
      },
    ],
  },
]);

/**
 * 处理餐次点击事件
 * @param payload 点击事件数据
 */
const handleMealClick = (payload: { canteen: CanteenData; meal: MealData }) => {
  console.log("餐次被点击:", payload);
  // 这里可以跳转到预约页面或显示预约弹窗
};

/**
 * 处理食堂卡片点击事件
 * @param canteen 被点击的食堂数据
 */
const handleCardClick = (canteen: CanteenData) => {
  console.log("食堂卡片被点击:", canteen);
  // 这里可以跳转到食堂详情页面
};
</script>

<style scoped lang="scss">
.home-page {
  width: 100%;
  min-height: 100vh;
  padding-bottom: var(--tabbar-height);
  background: linear-gradient(180deg, #3a88f5 0%, #f2f1f6 50%);
  overflow-x: hidden;
}

// 头部区域
.header-section {
  position: relative;
  padding: 20px 16px 40px;
  background: linear-gradient(135deg, #4a90e2 0%, #3a88f5 100%);
  color: white;
  overflow: hidden;

  // 头部内容
  .header-content {
    position: relative;
    z-index: 2;

    .page-title {
      font-size: 24px;
      font-weight: 600;
      margin: 0 0 8px 0;
      line-height: 1.2;
    }

    .page-subtitle {
      font-size: 14px;
      opacity: 0.9;
      margin: 0;
      line-height: 1.4;
    }
  }

  // 头部插画
  .header-illustration {
    position: absolute;
    top: 0;
    right: 0;
    width: 60%;
    height: 100%;
    z-index: 1;

    img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      opacity: 0.8;
    }
  }
}

// 内容区域
.content-section {
  flex: 1;
  padding: 20px 16px 20px;
  background: #f6f7fb;
  border-radius: 20px 20px 0 0;
  margin-top: -20px;
  position: relative;
  z-index: 3;
  min-height: calc(100vh - 200px);
}

// 食堂列表
.canteen-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

// 移动端适配
@media (max-width: 768px) {
  .header-section {
    padding: 16px 12px 32px;

    .header-content {
      .page-title {
        font-size: 22px;
      }

      .page-subtitle {
        font-size: 13px;
      }
    }
  }

  .content-section {
    padding: 16px 12px 16px;
    margin-top: -16px;
    border-radius: 16px 16px 0 0;
  }
}

// 超小屏幕适配
@media (max-width: 375px) {
  .header-section {
    padding: 14px 10px 28px;

    .header-content {
      .page-title {
        font-size: 20px;
      }

      .page-subtitle {
        font-size: 12px;
      }
    }
  }

  .content-section {
    padding: 14px 10px 14px;
    margin-top: -14px;
    border-radius: 14px 14px 0 0;
  }
}

// 状态栏适配（iPhone X及以上）
@supports (padding-top: env(safe-area-inset-top)) {
  .header-section {
    padding-top: calc(20px + env(safe-area-inset-top));
  }

  @media (max-width: 768px) {
    .header-section {
      padding-top: calc(16px + env(safe-area-inset-top));
    }
  }

  @media (max-width: 375px) {
    .header-section {
      padding-top: calc(14px + env(safe-area-inset-top));
    }
  }
}
</style>
