import { createRouterFlatModule, createRouterModule } from "../util";

const moduleName = "client";

export default createRouterModule({
  name: moduleName,
  options: {
    meta: {
      menu: false,
    },
  },
  children: [
    {
      path: "home",
      meta: {
        title: "首页",
      },
      component: () => import("@/views/client/home/<USER>"),
    },
    ...createRouterFlatModule({
      name: "home",
      children: [
        {
          path: "booking",
          component: () => import("@/views/client/home/<USER>"),
          meta: {
            title: "预约中心",
          },
        },
      ],
    }),
    {
      path: "diningCode",
      meta: {
        title: "就餐码",
      },
      component: () => import("@/views/client/diningCode/index.vue"),
    },
    {
      path: "personalCenter",
      meta: {
        title: "个人中心",
      },
      component: () => import("@/views/client/personalCenter/index.vue"),
    },
    ...createRouterFlatModule({
      name: "personalCenter",
      children: [
        {
          path: "myBooking",
          component: () =>
            import("@/views/client/personalCenter/myBooking.vue"),
          meta: {
            title: "我的预约",
          },
        },
        {
          path: "notifications",
          component: () =>
            import("@/views/client/personalCenter/notifications.vue"),
          meta: {
            title: "消息中心",
          },
        },
      ],
    }),
  ],
});
