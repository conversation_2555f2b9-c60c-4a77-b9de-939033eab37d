import type { RouteConfig } from 'vue-router'
import Vue from 'vue'
import VueRouter from 'vue-router'
import { getAppName } from '@/utils/env'

Vue.use(VueRouter)

// 批量导入./modules 目录下的所有 路由模块
const modules = require.context('./modules', false, /\.ts$/)

/**
 * 路由注册表，考虑到
 * createRouterModule 返回对象
 * createRouterFlatModule 返回数组
 * 所有需要使用 flat 进行扁平化
 */
const routes = modules.keys().map<RouteConfig[]>((path) => {
  return modules(path).default
}).flat(1)

const router = new VueRouter({
  mode: 'history',
  routes,
})

// 路由守卫 - 设置页面标题
router.beforeEach((to, _from, next) => {
  document.title = to.meta?.title || getAppName()
  next()
})

export default router
