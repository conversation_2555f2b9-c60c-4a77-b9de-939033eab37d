# Vue3 前端开发规范文档

## 目录结构规范

```bash
src
├── api
├── assets
├── components
├── layout
├── router
├── store
├── utils
├── views
└── App.vue
```

## 命名规范

### 1. 文件命名

-   组件文件名使用 PascalCase（大驼峰）: `UserProfile.vue`
-   路由页面组件使用 kebab-case（短横线）: `user-profile.vue`
-   工具函数文件使用 camelCase（小驼峰）: `formatDate.ts`

### 2. 组件命名

-   组件名应该始终是多个单词的，根组件 App 除外
-   使用 PascalCase 方式命名：`UserProfileInfo`

### 3. 变量命名

-   变量名使用 camelCase（小驼峰）
-   Boolean 类型变量使用 is/has/should 开头：`isVisible`、`hasPermission`
-   常量使用全大写下划线：`MAX_COUNT`

## 代码规范

### 1. 组件编写顺序

```vue
<script setup lang="ts">
// 组合式 API 使用 setup 语法糖
// 1. 导入
import { ref } from 'vue'
// 2. 类型定义
interface Props {
title: string
}
// 3. 定义 props、emits
const props = defineProps<Props>()
const emit = defineEmits<{
(e: 'update'): void
}>()
// 4. 响应式数据
const count = ref(0)
// 5. 计算属性
const doubleCount = computed(() => count.value 2)
// 6. 方法
const handleClick = () => {
count.value++
}
// 7. 生命周期钩子
onMounted(() => {
// 初始化逻辑
})
</script>
```

### 2. 模板规范

-   使用 v-for 时必须绑定 key
-   v-if 和 v-for 不要用在同一个元素上
-   组件模板应该只包含简单的表达式
-   属性值使用双引号 `""`

### 3. 样式规范

-   使用 scoped 或 CSS Modules 避免样式污染
-   class 命名使用 kebab-case
-   优先使用 class 而不是 style 作为样式处理方式

```vue
<style scoped>
.user-profile {
    /* 推荐使用 class 命名方式 */
}
</style>
```

### 4. TypeScript 规范

-   优先使用 `interface` 而不是 `type`
-   必须为 props 定义类型
-   为函数定义返回类型
-   使用 `type` 定义联合类型和交叉类型

### 5. 性能优化

-   合理使用 v-show 和 v-if
-   长列表使用虚拟滚动
-   使用 keep-alive 缓存组件
-   路由懒加载
-   大型组件拆分为小组件

### 6. Git 提交规范

提交信息格式：`<type>: <subject>`

type 类型：

-   feat: 新功能
-   fix: 修复 bug
-   docs: 文档修改
-   style: 代码格式修改
-   refactor: 代码重构
-   test: 测试用例修改
-   chore: 其他修改

## 项目配置规范

### 1. ESLint 配置

-   使用 ESLint + Prettier 进行代码格式化
-   提交前强制代码格式化
-   建议使用 VS Code 的 ESLint 插件

### 2. 编辑器配置

推荐使用 VS Code，并安装以下插件：

-   Volar
-   ESLint
-   Prettier
-   GitLens

### 3. 依赖管理

-   使用 pnpm/yarn 作为包管理器
-   锁定依赖版本
-   定期更新依赖包

## 注释规范

### 1. 文件注释

```ts
/**
 * @description 文件用途描述
 * <AUTHOR>
 * @date 2024-03-xx
 */
```

### 2. 函数注释

```ts
/**
 * 函数描述
 * @param {string} param1 - 参数1的说明
 * @param {number} param2 - 参数2的说明
 * @returns {boolean} 返回值的说明
 */
```

### 3. 组件注释

```vue
<!--
  @description 组件描述
  @example
  <MyComponent
    :prop1="value1"
    :prop2="value2"
  />
-->
```

## 最佳实践

1. 优先使用组合式 API
2. 及时抽取可复用的组合式函数
3. 保持组件的单一职责
4. 使用 TypeScript 提供类型安全
5. 遵循 Vue3 官方推荐的风格指南
6. 定期进行代码审查
7. 保持文档的及时更新

## 发布规范

1. 版本号遵循语义化版本（Semantic Versioning）
2. 每次发布都要有更新日志（CHANGELOG）
3. 发布前进行完整的测试
4. 保持文档的同步更新

## 其他规范

1. 代码审查（Code Review）要求
2. 测试覆盖率要求
3. 性能指标要求
4. 安全规范要求

---

本规范文档会随着项目发展持续更新，团队成员应该定期查看并遵循最新规范。
