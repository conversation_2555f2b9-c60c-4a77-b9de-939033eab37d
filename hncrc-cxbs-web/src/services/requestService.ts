import { storageLocal } from '@pureadmin/utils';
import axios, { type AxiosRequestConfig } from 'axios';
import { ElLoading, ElMessage, ElMessageBox } from 'element-plus';

import { useMessage } from '@/hooks/web/useMessage';
import { useUserStoreHook } from '@/store/modules/user';
import { TokenKey } from '@/utils/auth';
import { readFileAsText } from '@/utils/file';

const instance = axios.create({
    withCredentials: false,
    baseURL: '/api',
    timeout: 60 * 1000,
});

const message = useMessage();
let loadingInstance;

instance.interceptors.request.use(
    (config) => {
        const token = storageLocal().getItem<string | null>(TokenKey);
        //// console.log(token, window.location.pathname);
        if (token) {
            // // console.l
            config.headers['Authorization'] = token;
        }
        if (!loadingInstance) loadingInstance = ElLoading.service({ text: '加载中...' });
        // console.info('开始请求', config.url, config.data || config.params);
        return config;
    },
    (error) => {
        loadingInstance && loadingInstance.close();
        // console.log('request', error);
        return Promise.reject(error);
    },
);

instance.interceptors.response.use(
    async (response) => {
        ElMessageBox.close();
        loadingInstance && loadingInstance.close();
        // console.info('结束请求', response.config.url, response.data);
        const res = response.data;
        const reLoginCode = [
            '50009',
            '500010',
            '401',
            '50008',
            '50012',
            '500011',
            '50014',
            '5001001',
        ];
        const responseCode = response?.data?.code || response?.data?.data?.code || response.status;
        if (response.status === 501 || reLoginCode.includes(`${responseCode}`)) {
            message.confirm('登录信息已过期，请重新打开该页面').finally(() => {
                useUserStoreHook().logOut();
                // window.location.href = '/login';
                // router.replace('/login');
            });

            return Promise.reject(res.msg || 'Error');
        }
        // 网络请求响应异常
        if (response.status !== 200) {
            ElMessage.error({
                message: '当前系统繁忙，请稍后再试',
                grouping: true,
            });
            return Promise.reject(res.msg || 'Error');
        }

        // 处理返回数据为Blob的情况
        if (responseCode === 200 && response.config.responseType === 'blob') {
            // 说明返回的不是数据流，下载失败了
            if (response.data.type === 'application/json') {
                const errorRes = await readFileAsText(response.data);
                return Promise.reject(JSON.parse(errorRes));
            } else {
                const filename = response.headers['content-disposition']
                    .split(';')[1]
                    // HACK: 可能文件名的key不一样，临时这样处理，后续看看更好的处理方法
                    .replace('filename=', '')
                    .replace('fileName=', '')
                    .trim();
                const retRes = {
                    data: res,
                    fileName: filename && decodeURIComponent(filename),
                    fileType: response.headers['content-type'].trim(),
                };
                // return Promise.resolve(res);
                return Promise.resolve(retRes);
            }
        }
        if (res.code === 0 || res.code === '0') {
            if (res.data?.code === '500011') {
                // console.log(res.data);
                ElMessageBox.confirm(res.data.msg, '登录失败', {
                    confirmButtonText: '重新登录',
                    cancelButtonText: '取消',
                    type: 'warning',
                })
                    .then(() => {
                        useUserStoreHook().logOut();
                        window.location.href = '/login';
                    })
                    .catch(() => {
                        useUserStoreHook().logOut();
                        window.location.href = '/login';
                    });
            }
            return Promise.resolve(res.data);
        } else if (res.code !== 200) {
            ElMessage.error({
                message: res.msg || '系统发生异常，请联系管理员。',
                grouping: true,
            });
            return Promise.reject(res.msg || '系统发生异常，请联系管理员。');
        }

        return Promise.reject(res);
    },
    (error) => {
        loadingInstance && loadingInstance.close();
        if (JSON.stringify(error).indexOf('timeout')) {
            ElMessage.error({
                message: '请求超时，请重试',
                grouping: true,
            });
        }
        ElMessage.error({
            message: '当前系统繁忙，请稍后再试',
            grouping: true,
        });
        return Promise.reject(error);
    },
);

function requestService<P = AxiosRequestConfig['data'], R = any>(
    path: string,
    params?: P,
    options: AxiosRequestConfig = {},
) {
    const realOptions = {
        url: path,
        method: 'POST',
        ...options,
    };

    if (realOptions.method.toUpperCase() === 'GET') {
        realOptions.params = params;
    } else {
        realOptions.data = params;
    }
    // NOTE: 20241222 有些接口设计会DELETE会从query中传递参数，这里做特殊处理
    if (realOptions.method.toUpperCase() === 'DELETE') {
        realOptions.params = params;
    }

    return instance(realOptions) as Promise<R>;
}
export default requestService;
